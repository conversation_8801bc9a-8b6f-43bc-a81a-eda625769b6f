#!/usr/bin/env node

import {
  McpServer,
  ResourceTemplate,
} from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

import { FincloudUIIndexer } from "./indexers/component-indexer.js";
import { CompletionProvider } from "./completions/completion-provider.js";
import { DocumentationGenerator } from "./documentation/doc-generator.js";
import { CodeAnalyzer } from "./analysis/code-analyzer.js";
import { ConfigManager } from "./utils/config-manager.js";

class FincloudUIMCPServer {
  private server: McpServer;
  private indexer: FincloudUIIndexer;
  private completionProvider: CompletionProvider;
  private docGenerator: DocumentationGenerator;
  private codeAnalyzer: CodeAnalyzer;
  private configManager: ConfigManager;

  constructor() {
    this.server = new McpServer({
      name: "fincloud-ui-mcp-server",
      version: "1.0.0",
    });

    this.configManager = new ConfigManager();
    this.indexer = new FincloudUIIndexer(this.configManager);
    this.completionProvider = new CompletionProvider(this.indexer);
    this.docGenerator = new DocumentationGenerator(this.indexer);
    this.codeAnalyzer = new CodeAnalyzer(this.indexer);

    this.setupHandlers();
  }

  private setupHandlers() {
    // Register static resources
    this.server.registerResource(
      "components",
      "fincloud://components",
      {
        title: "All Fincloud UI Components",
        description:
          "Complete registry of all UI components with metadata, API details, and relationships",
        mimeType: "application/json",
      },
      async (uri) => ({
        contents: [
          {
            uri: uri.href,
            mimeType: "application/json",
            text: JSON.stringify(this.indexer.getComponentRegistry(), null, 2),
          },
        ],
      })
    );

    this.server.registerResource(
      "components-summary",
      "fincloud://components/summary",
      {
        title: "Components Summary",
        description:
          "Lightweight summary of all components (names, categories, selectors only)",
        mimeType: "application/json",
      },
      async (uri) => ({
        contents: [
          {
            uri: uri.href,
            mimeType: "application/json",
            text: JSON.stringify(this.indexer.getComponentsSummary(), null, 2),
          },
        ],
      })
    );

    this.server.registerResource(
      "utils",
      "fincloud://utils",
      {
        title: "All Fincloud Utils",
        description: "Complete registry of all utility functions and services",
        mimeType: "application/json",
      },
      async (uri) => ({
        contents: [
          {
            uri: uri.href,
            mimeType: "application/json",
            text: JSON.stringify(this.indexer.getUtilsRegistry(), null, 2),
          },
        ],
      })
    );

    this.server.registerResource(
      "examples",
      "fincloud://examples",
      {
        title: "Code Examples",
        description: "Code examples for components and utilities",
        mimeType: "application/json",
      },
      async (uri) => ({
        contents: [
          {
            uri: uri.href,
            mimeType: "application/json",
            text: JSON.stringify(this.indexer.getExamples(), null, 2),
          },
        ],
      })
    );

    // Register dynamic resource templates
    this.server.registerResource(
      "component",
      new ResourceTemplate("fincloud://components/{componentName}", {
        list: undefined,
      }),
      {
        title: "Fincloud UI Component",
        description:
          "Individual component with complete metadata, API details, relationships, and examples",
        mimeType: "application/json",
      },
      async (uri, { componentName }) => {
        const componentNameStr = Array.isArray(componentName)
          ? componentName[0]
          : componentName;
        const component = this.indexer.getComponentMetadata(componentNameStr);

        if (!component) {
          const error = new Error(`Component not found: ${componentNameStr}`);
          (error as any).code = -32002; // Resource not found error code
          (error as any).data = { uri: uri.href };
          throw error;
        }

        const fullComponent =
          this.indexer.getComponentRegistry()[componentNameStr];
        return {
          contents: [
            {
              uri: uri.href,
              mimeType: "application/json",
              text: JSON.stringify(fullComponent, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerResource(
      "utility",
      new ResourceTemplate("fincloud://utils/{utilityName}", {
        list: undefined,
      }),
      {
        title: "Fincloud Utility",
        description:
          "Individual utility function or service with documentation and usage examples",
        mimeType: "application/json",
      },
      async (uri, { utilityName }) => {
        const utilityNameStr = Array.isArray(utilityName)
          ? utilityName[0]
          : utilityName;
        const utilsRegistry = this.indexer.getUtilsRegistry();

        // Check if it's a function
        if (utilsRegistry.functions[utilityNameStr]) {
          return {
            contents: [
              {
                uri: uri.href,
                mimeType: "application/json",
                text: JSON.stringify(
                  utilsRegistry.functions[utilityNameStr],
                  null,
                  2
                ),
              },
            ],
          };
        }

        // Check if it's a service
        if (utilsRegistry.services[utilityNameStr]) {
          return {
            contents: [
              {
                uri: uri.href,
                mimeType: "application/json",
                text: JSON.stringify(
                  utilsRegistry.services[utilityNameStr],
                  null,
                  2
                ),
              },
            ],
          };
        }

        // Utility not found
        const error = new Error(`Utility not found: ${utilityNameStr}`);
        (error as any).code = -32002; // Resource not found error code
        (error as any).data = { uri: uri.href };
        throw error;
      }
    );

    // Register tools
    this.server.registerTool(
      "get_component_completions",
      {
        title: "Component Completions",
        description:
          "Get code completion suggestions for Angular templates and TypeScript",
        inputSchema: {
          context: z
            .string()
            .describe("The code context where completion is requested"),
          position: z.object({
            line: z.number(),
            character: z.number(),
          }),
          filePath: z
            .string()
            .optional()
            .describe("Path to the file being edited"),
        },
      },
      async ({ context, position, filePath }) => {
        const completions = await this.completionProvider.getCompletions({
          context,
          position,
          filePath: filePath || "unknown",
        });
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(completions, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "get_component_documentation",
      {
        title: "Component Documentation",
        description: "Generate comprehensive documentation for a component",
        inputSchema: {
          componentName: z
            .string()
            .describe("Name of the component to document"),
          includeExamples: z
            .boolean()
            .optional()
            .default(true)
            .describe("Whether to include code examples"),
        },
      },
      async ({ componentName, includeExamples = true }) => {
        const documentation = await this.docGenerator.generateDocumentation(
          componentName,
          { includeExamples }
        );
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(documentation, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "analyze_code",
      {
        title: "Code Analysis",
        description:
          "Analyze code for Fincloud UI component usage, deprecated components, and Fincloud-specific best practices",
        inputSchema: {
          filePath: z
            .string()
            .optional()
            .describe("Path to the file to analyze"),
          code: z
            .string()
            .optional()
            .describe(
              "Code content to analyze (optional if filePath provided)"
            ),
        },
      },
      async ({ filePath, code }) => {
        const analysis = await this.codeAnalyzer.analyzeCode({
          filePath,
          code,
        });
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(analysis, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "search_components",
      {
        title: "Search Components",
        description: "Search for components by name, functionality, or usage",
        inputSchema: {
          query: z.string().describe("Search query"),
          category: z
            .enum([
              "form",
              "layout",
              "navigation",
              "data-display",
              "feedback",
              "all",
            ])
            .optional()
            .default("all")
            .describe("Component category to search within"),
          includeUtils: z
            .boolean()
            .optional()
            .default(false)
            .describe("Whether to include utility functions in search"),
        },
      },
      async ({ query, category = "all", includeUtils = false }) => {
        const results = await this.indexer.searchComponents(query, {
          category,
          includeUtils,
        });
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(results, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "get_import_suggestions",
      {
        title: "Import Suggestions",
        description: "Get import path suggestions for components and utilities",
        inputSchema: {
          symbol: z.string().describe("Symbol name to find import for"),
          currentFilePath: z
            .string()
            .optional()
            .describe("Path of the current file"),
        },
      },
      async ({ symbol }) => {
        const suggestions = await this.completionProvider.getImportSuggestions(
          symbol
        );
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(suggestions, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "get_related_components",
      {
        title: "Related Components",
        description:
          "Find components related to or similar to a given component",
        inputSchema: {
          componentName: z
            .string()
            .describe("Name of the component to find related components for"),
          relationshipType: z
            .enum(["similar", "dependencies", "dependents", "all"])
            .optional()
            .default("all")
            .describe("Type of relationship to search for"),
        },
      },
      async ({ componentName, relationshipType = "all" }) => {
        const related = await this.indexer.getRelatedComponents(
          componentName,
          relationshipType
        );
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(related, null, 2),
            },
          ],
        };
      }
    );
  }

  async run() {
    try {
      // Initialize the indexer
      await this.indexer.initialize();

      const transport = new StdioServerTransport();
      await this.server.connect(transport);
    } catch (error) {
      console.error("Error during server startup:");
      console.error("Error name:", (error as any)?.name || "Unknown");
      console.error("Error message:", (error as any)?.message || "No message");
      console.error("Error stack:", (error as any)?.stack || "No stack");
      throw error; // Re-throw to be caught by the outer catch
    }
  }
}

const server = new FincloudUIMCPServer();
server.run().catch((error) => {
  console.error("=== FATAL SERVER ERROR ===");
  console.error("Error type:", typeof error);
  console.error("Error name:", error?.name || "Unknown");
  console.error("Error message:", error?.message || "No message");
  console.error("Error code:", error?.code || "No code");
  console.error("Full error object:", JSON.stringify(error, null, 2));
  console.error("Error stack:");
  console.error(error?.stack || "No stack trace available");
  console.error("=== END ERROR DETAILS ===");
  process.exit(1);
});
